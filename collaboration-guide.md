# 🤝 智能协作环境使用指南

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐
│   您的RTX 3060   │    │  Mac mini M4    │
│   ************* │    │  192.168.1.xxx  │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
            ┌─────────────────┐
            │   协作服务中心   │
            │  (您的机器托管)  │
            └─────────────────┘
                     │
        ┌────────────┼────────────┐
        │            │            │
   ┌─────────┐  ┌─────────┐  ┌─────────┐
   │ Nextcloud│  │  Gitea  │  │ 其他服务 │
   │文件协作  │  │代码协作  │  │AI/GPU等 │
   └─────────┘  └─────────┘  └─────────┘
```

## 🚀 快速开始

### 1. 部署服务
```bash
chmod +x setup.sh
./setup.sh
```

### 2. 初始配置

#### Nextcloud配置
1. 访问: http://*************:8080
2. 登录: admin / admin123
3. 安装推荐应用:
   - Files (文件管理)
   - Calendar (日历)
   - Contacts (联系人)
   - Talk (即时通讯)
   - Deck (看板管理)

#### Gitea配置
1. 访问: http://*************:3000
2. 完成初始设置
3. 创建组织和仓库

## 🔄 协作工作流

### 场景1: AI项目开发

#### 在您的RTX 3060机器上:
```bash
# 1. 克隆项目
git clone http://*************:3000/your-org/ai-project.git
cd ai-project

# 2. 创建GPU训练环境
python -m venv venv-gpu
source venv-gpu/bin/activate
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 3. 运行GPU训练
python train.py --gpu --epochs 100

# 4. 上传结果到Nextcloud
curl -u admin:admin123 -T model.pth http://*************:8080/remote.php/dav/files/admin/models/
```

#### 在Mac mini M4上:
```bash
# 1. 同步代码
git clone http://*************:3000/your-org/ai-project.git
cd ai-project

# 2. 数据预处理
python preprocess.py --input data/ --output processed/

# 3. 提交代码
git add .
git commit -m "Add data preprocessing"
git push origin main

# 4. 下载训练好的模型
curl -u admin:admin123 http://*************:8080/remote.php/dav/files/admin/models/model.pth -o model.pth
```

### 场景2: 文档协作

#### 共享文件夹结构:
```
Nextcloud/
├── Projects/
│   ├── AI-Research/
│   │   ├── Papers/
│   │   ├── Datasets/
│   │   └── Models/
│   └── Development/
│       ├── Code-Reviews/
│       └── Documentation/
├── Resources/
│   ├── GPU-Benchmarks/
│   └── Performance-Data/
└── Shared/
    ├── Meeting-Notes/
    └── Task-Lists/
```

## 🔧 高级配置

### GPU任务调度
```bash
# 创建GPU任务队列脚本
cat > gpu-queue.sh << 'EOF'
#!/bin/bash
QUEUE_DIR="/path/to/nextcloud/GPU-Queue"
RESULTS_DIR="/path/to/nextcloud/GPU-Results"

while true; do
    for task in $QUEUE_DIR/*.json; do
        if [ -f "$task" ]; then
            echo "处理任务: $task"
            python gpu-worker.py "$task"
            mv "$task" "$RESULTS_DIR/"
        fi
    done
    sleep 10
done
EOF
```

### 自动同步脚本
```bash
# Mac mini上的同步脚本
cat > sync-from-gpu.sh << 'EOF'
#!/bin/bash
# 同步GPU计算结果
rsync -avz ben@*************:/path/to/results/ ./local-results/

# 同步到Nextcloud
curl -u admin:admin123 -T ./local-results/* \
  http://*************:8080/remote.php/dav/files/admin/results/
EOF
```

## 📊 监控和管理

### 服务监控
```bash
# 查看所有服务状态
./manage.sh status

# 查看特定服务日志
./manage.sh logs nextcloud
./manage.sh logs gitea

# 重启服务
./manage.sh restart
```

### 性能监控
```bash
# GPU使用率
nvidia-smi -l 1

# 容器资源使用
docker stats

# 磁盘空间
df -h
```

## 🔐 安全配置

### 1. 防火墙设置
```bash
# 只允许内网访问
sudo ufw allow from 192.168.1.0/24 to any port 3000
sudo ufw allow from 192.168.1.0/24 to any port 8080
sudo ufw allow from 192.168.1.0/24 to any port 9000
```

### 2. 备份策略
```bash
# 每日自动备份
echo "0 2 * * * /path/to/manage.sh backup" | crontab -
```

## 💡 最佳实践

### 1. 任务分工
- **RTX 3060**: GPU密集型任务、模型训练、游戏测试
- **Mac mini M4**: 代码开发、数据处理、项目管理

### 2. 文件管理
- 大文件(>100MB): 直接存储在RTX 3060机器
- 代码文件: 通过Gitea管理
- 文档资料: 通过Nextcloud共享

### 3. 通信协作
- 使用Nextcloud Talk进行即时沟通
- 使用Gitea Issues跟踪任务
- 使用Nextcloud Deck管理项目进度

## 🚀 扩展功能

### 添加更多服务
```yaml
# 在docker-compose.yml中添加
  jupyter:
    image: jupyter/tensorflow-notebook
    ports:
      - "8888:8888"
    volumes:
      - ./jupyter:/home/<USER>/work
    environment:
      - JUPYTER_ENABLE_LAB=yes
```

### API集成
```python
# Python示例: 自动上传训练结果
import requests

def upload_to_nextcloud(file_path, remote_path):
    url = f"http://*************:8080/remote.php/dav/files/admin/{remote_path}"
    with open(file_path, 'rb') as f:
        response = requests.put(url, data=f, auth=('admin', 'admin123'))
    return response.status_code == 201
```
