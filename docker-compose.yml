version: '3.8'

services:
  # Gitea - 代码协作
  gitea:
    image: gitea/gitea:1.21
    container_name: gitea
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - GITEA__database__DB_TYPE=postgres
      - GITEA__database__HOST=gitea-db:5432
      - GITEA__database__NAME=gitea
      - GITEA__database__USER=gitea
      - GITEA__database__PASSWD=gitea_password
    restart: always
    networks:
      - gitea
    volumes:
      - ./gitea:/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "3000:3000"
      - "222:22"
    depends_on:
      - gitea-db

  gitea-db:
    image: postgres:14
    container_name: gitea-db
    restart: always
    environment:
      - POSTGRES_USER=gitea
      - POSTGRES_PASSWORD=gitea_password
      - POSTGRES_DB=gitea
    networks:
      - gitea
    volumes:
      - ./gitea-db:/var/lib/postgresql/data

  # Nextcloud - 文件协作
  nextcloud:
    image: nextcloud:28
    container_name: nextcloud
    restart: always
    ports:
      - "8080:80"
    environment:
      - POSTGRES_HOST=nextcloud-db
      - POSTGRES_DB=nextcloud
      - POSTGRES_USER=nextcloud
      - POSTGRES_PASSWORD=nextcloud_password
      - NEXTCLOUD_ADMIN_USER=admin
      - NEXTCLOUD_ADMIN_PASSWORD=admin123
    volumes:
      - ./nextcloud:/var/www/html
      - ./nextcloud-data:/var/www/html/data
    depends_on:
      - nextcloud-db
    networks:
      - nextcloud

  nextcloud-db:
    image: postgres:14
    container_name: nextcloud-db
    restart: always
    environment:
      - POSTGRES_DB=nextcloud
      - POSTGRES_USER=nextcloud
      - POSTGRES_PASSWORD=nextcloud_password
    volumes:
      - ./nextcloud-db:/var/lib/postgresql/data
    networks:
      - nextcloud

  # Redis - 缓存加速
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: always
    networks:
      - nextcloud

  # Nginx - 反向代理
  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - nextcloud
      - gitea
    networks:
      - nextcloud
      - gitea

  # Portainer - 容器管理
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    restart: always
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./portainer:/data

  # Watchtower - 自动更新
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_SCHEDULE=0 0 4 * * *

networks:
  gitea:
    driver: bridge
  nextcloud:
    driver: bridge

volumes:
  gitea-data:
  nextcloud-data:
  nextcloud-db-data:
  gitea-db-data:
