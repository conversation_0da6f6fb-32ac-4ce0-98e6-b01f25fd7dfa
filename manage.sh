#!/bin/bash

case "$1" in
    start)
        echo "启动所有服务..."
        docker-compose up -d
        ;;
    stop)
        echo "停止所有服务..."
        docker-compose down
        ;;
    restart)
        echo "重启所有服务..."
        docker-compose restart
        ;;
    logs)
        if [ -n "$2" ]; then
            docker-compose logs -f "$2"
        else
            docker-compose logs -f
        fi
        ;;
    status)
        docker-compose ps
        ;;
    update)
        echo "更新所有镜像..."
        docker-compose pull
        docker-compose up -d
        ;;
    backup)
        echo "备份数据..."
        tar -czf "backup-$(date +%Y%m%d-%H%M%S).tar.gz" gitea gitea-db nextcloud nextcloud-data nextcloud-db
        echo "备份完成: backup-$(date +%Y%m%d-%H%M%S).tar.gz"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|logs [service]|status|update|backup}"
        echo ""
        echo "示例:"
        echo "  $0 start          # 启动所有服务"
        echo "  $0 logs nextcloud # 查看Nextcloud日志"
        echo "  $0 backup         # 备份数据"
        ;;
esac
