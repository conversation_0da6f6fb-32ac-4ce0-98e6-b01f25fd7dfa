#!/bin/bash

# 智能协作环境部署脚本
echo "🚀 开始部署智能协作环境..."

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p {gitea,gitea-db,nextcloud,nextcloud-data,nextcloud-db,nginx/conf.d,ssl,portainer}

# 设置权限
echo "🔐 设置目录权限..."
sudo chown -R $USER:$USER .
chmod -R 755 .

# 启动服务
echo "🐳 启动Docker服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "🎉 部署完成！访问信息："
echo "================================"
echo "📁 Nextcloud (文件协作):"
echo "   URL: http://192.168.1.222:8080"
echo "   管理员: admin / admin123"
echo ""
echo "🔧 Gitea (代码协作):"
echo "   URL: http://192.168.1.222:3000"
echo "   需要初始化设置"
echo ""
echo "🐳 Portainer (容器管理):"
echo "   URL: http://192.168.1.222:9000"
echo ""
echo "📊 服务状态监控:"
echo "   docker-compose logs -f [服务名]"
echo "================================"

# 创建快速操作脚本
cat > manage.sh << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "启动所有服务..."
        docker-compose up -d
        ;;
    stop)
        echo "停止所有服务..."
        docker-compose down
        ;;
    restart)
        echo "重启所有服务..."
        docker-compose restart
        ;;
    logs)
        if [ -n "$2" ]; then
            docker-compose logs -f "$2"
        else
            docker-compose logs -f
        fi
        ;;
    status)
        docker-compose ps
        ;;
    update)
        echo "更新所有镜像..."
        docker-compose pull
        docker-compose up -d
        ;;
    backup)
        echo "备份数据..."
        tar -czf "backup-$(date +%Y%m%d-%H%M%S).tar.gz" gitea gitea-db nextcloud nextcloud-data nextcloud-db
        echo "备份完成: backup-$(date +%Y%m%d-%H%M%S).tar.gz"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|logs [service]|status|update|backup}"
        echo ""
        echo "示例:"
        echo "  $0 start          # 启动所有服务"
        echo "  $0 logs nextcloud # 查看Nextcloud日志"
        echo "  $0 backup         # 备份数据"
        ;;
esac
EOF

chmod +x manage.sh

echo ""
echo "💡 快速管理命令已创建: ./manage.sh"
echo "   ./manage.sh start   # 启动服务"
echo "   ./manage.sh stop    # 停止服务"
echo "   ./manage.sh logs    # 查看日志"
echo "   ./manage.sh backup  # 备份数据"
